<?php init_head(); ?>

<div id="wrapper">
   <div class="content">
      <div class="row">
         <div class="col-md-12" id="small-table">
            <div class="panel_s">
               <div class="panel-body">
                <?php echo form_hidden('item_id',$item_id); ?>
                  <div class="row">
                     <div class="col-md-12">
                      <h4 class="no-margin font-bold"><i class="fa fa-clone menu-icon menu-icon" aria-hidden="true"></i> <?php echo _l($title); ?></h4>
                      <br>

                    </div>
                  </div>
                  <div class="row row-margin-bottom">
                    <div class="col-md-5  ">
                        <?php if (has_permission('purchase_items', '', 'create') || is_admin()) { ?>

                          <!-- dung cho add 1 -->
                        <a href="#" onclick="new_commodity_item(); return false;" class="btn btn-info pull-left display-block mr-4 button-margin-r-b" data-toggle="sidebar-right" data-target=".commodity_list-add-edit-modal">
                            <?php echo _l('add'); ?>
                        </a>

                        <a href="<?php echo admin_url('purchase/import_xlsx_commodity'); ?>" class="btn btn-success pull-left display-block mr-4 button-margin-r-b">
                            <?php echo _l('import_items'); ?>
                        </a>
                        <?php } ?>
                    </div>
                    
                     <div class="col-md-1 pull-right">
                        <a href="#" class="btn btn-default pull-right btn-with-tooltip toggle-small-view hidden-xs" onclick="toggle_small_view_proposal('.proposal_sm','#proposal_sm_view'); return false;" data-toggle="tooltip" title="<?php echo _l('invoices_toggle_table_tooltip'); ?>"><i class="fa fa-angle-double-left"></i></a>
                    </div>
                    </div>

                    <div class="modal bulk_actions" id="table_commodity_list_bulk_actions" tabindex="-1" role="dialog">
                        <div class="modal-dialog" role="document">
                           <div class="modal-content">
                              <div class="modal-header">
                                 <h4 class="modal-title"><?php echo _l('bulk_actions'); ?></h4>
                                 <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                              </div>
                              <div class="modal-body">

                                 <div class="checkbox checkbox-danger">
                                  <div class="row">

                                 <?php if(has_permission('purchase_items','','delete') || is_admin()){ ?>
                                  <div class="col-md-4">
                                    <div class="form-group">
                                      <input type="checkbox" name="mass_delete" id="mass_delete">
                                      <label for="mass_delete"><?php echo _l('mass_delete'); ?></label>
                                    </div>
                                  </div>
                                 <?php } ?>

                                   
                                  </div>

                                  <div class="row">
                                    <?php if(has_permission('purchase_items','','create') || is_admin()){ ?>
                                  <div class="col-md-4">
                                    <div class="form-group">
                                      <input type="checkbox" name="clone_items" id="clone_items">
                                      <label for="clone_items"><?php echo _l('clone_this_items'); ?></label>
                                    </div>
                                  </div>
                                 <?php } ?>
                                    
                                  </div>

                                 <?php if(has_permission('purchase_items','','edit') || is_admin()){ ?>
                                  <div class="row">
                                    <div class="col-md-5">
                                      <div class="form-group">

                                        <input type="checkbox" name="change_item_selling_price" id="change_item_selling_price" >
                                        <label for="change_item_selling_price"><?php echo _l('change_item_selling_price'); ?></label>
                                      </div>
                                    </div>
                                    <div class="col-md-6">
                                      <div class="form-group">

                                        <div class="input-group" id="discount-total">
                                          <input type="number" class="form-control text-right" min="0" max="100" name="selling_price" value="">
                                          <div class="input-group-addon">
                                            <div class="dropdown">
                                             <span class="discount-type-selected">
                                              %
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                  </div>
                                </div>

                                <div class="row">
                                  <div class="col-md-5">
                                    <div class="form-group">

                                      <input type="checkbox" name="change_item_purchase_price" id="change_item_purchase_price">
                                      <label for="change_item_purchase_price"><?php echo _l('change_item_purchase_price'); ?></label>
                                    </div>
                                  </div>
                                  <div class="col-md-6">
                                    <div class="form-group">

                                      <div class="input-group" id="discount-total">
                                        <input type="number" class="form-control text-right" min="0" max="100" name="b_purchase_price" value="">
                                        <div class="input-group-addon">
                                          <div class="dropdown">
                                           <span class="discount-type-selected">
                                            %
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                                 <?php } ?>
                                 
                                 </div>
                                 
                              </div>
                              <div class="modal-footer">
                                 <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>

                                 <?php if(has_permission('purchase_items','','delete') || is_admin()){ ?>
                                 <a href="#" class="btn btn-info" onclick="purchase_delete_bulk_action(this); return false;"><?php echo _l('confirm'); ?></a>
                                  <?php } ?>
                              </div>
                           </div>
                          
                        </div>
                        
                     </div>

                    <a href="#"  onclick="staff_bulk_actions(); return false;" data-toggle="modal" data-table=".table-table_item_list" data-target="#leads_bulk_actions" class=" hide bulk-actions-btn table-btn"><?php echo _l('bulk_actions'); ?></a>
                      <?php
                       $table_data = array(
                        '<span class="hide"> - </span><div class="checkbox mass_select_all_wrap"><input type="checkbox" id="mass_select_all" data-to-table="table_item_list"><label></label></div>',
                        _l('_images'),
                        _l('commodity_code'),
                        _l('commodity_name'),
                        _l('groups'),
                        _l('unit_name'),
                        _l('rate'),
                        _l('purchase_price'),
                        _l('tax'),
                        _l('from_vendor'),
                        );

                      
                        $cf = get_custom_fields('items',array('show_on_table'=>1));
                        foreach($cf as $custom_field) {
                          array_push($table_data,$custom_field['name']);
                        }

                        render_datatable($table_data,'table_item_list',['proposal_sm' => 'proposal_sm'],
                        array(
                          'proposal_sm' => 'proposal_sm',
                           'id'=>'table-table_item_list',
                           'data-last-order-identifier'=>'table_item_list',
                           'data-default-order'=>get_table_last_order('table_item_list'),
                         )); 

                  ?>
               </div>
            </div>
         </div>
         <div class="col-md-7 small-table-right-col">
            <div id="proposal_sm_view" class="hide">
            </div>
         </div>
      </div>
   </div>
   
</div>


    <div class="modal" id="purchase_type" tabindex="-1" role="dialog">
    <div class="modal-dialog ht-dialog-width">

          <?php echo form_open_multipart(admin_url('purchase/add_commodity_list'), array('id'=>'add_purchase_type')); ?>
          <div class="modal-content" >
                <div class="modal-header">
                  <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>

                    <h4 class="modal-title">
                        <span class="add-title"><?php echo _l('add'); ?></span>
                    </h4>
                   
                </div>
                <div class="modal-body">
                  <div class="row">
                    <div class="col-md-12">
                         <div id="purchase_type_id">
                         </div>   
                     <div class="form"> 
                        <div class="col-md-12" id="add_handsontable">
                        </div>
                          <?php echo form_hidden('hot_purchase_type'); ?>
                    </div>
                    </div>
                    </div>
                </div>
                <div class="modal-footer">
                     <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                     <button id="latch_assessor" type="button" class="btn btn-info intext-btn" onclick="add_purchase_type(this); return false;" ><?php echo _l('submit'); ?></button>
                </div>
                <?php echo form_close(); ?>
              </div>
              </div>
          </div>


  <!-- add one commodity list sibar start-->       

    <div class="modal" id="commodity_list-add-edit" tabindex="-1" role="dialog">
    <div class="modal-dialog ht-dialog-width">

        <?php echo form_open_multipart(admin_url('purchase/commodity_list_add_edit'),array('class'=>'commodity_list-add-edit','autocomplete'=>'off')); ?>

      <div class="modal-content">

            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                <h4 class="modal-title">
                    <span class="edit-commodity-title"><?php echo _l('edit_item'); ?></span>
                    <span class="add-commodity-title"><?php echo _l('add_item'); ?></span>
                </h4>
            </div>

            <div class="modal-body">
                <div id="commodity_item_id"></div>
                <!-- interview process start -->
                  <div role="tabpanel" class="tab-pane active" id="interview_infor">

                            <div class="row">
                                <div class="col-md-6">
                                    <?php echo render_input('commodity_code', 'commodity_code'); ?>
                                </div>
                                <div class="col-md-6">
                                  <?php echo render_input('description', 'commodity_name'); ?>
                                </div>
                                
                            </div>

                            <div class="row">
                               <div class="col-md-4">
                                     <?php echo render_input('commodity_barcode', 'commodity_barcode','','text'); ?>
                                </div>
                              <div class="col-md-4">
                                <a href="#" class="pull-right display-block input_method"><i class="fa fa-question-circle skucode-tooltip"  data-toggle="tooltip" title="" data-original-title="<?php echo _l('commodity_sku_code_tooltip'); ?>"></i></a>
                                <?php echo render_input('sku_code', 'sku_code','',''); ?>
                              </div>
                              <div class="col-md-4">
                                <?php echo render_input('sku_name', 'sku_name'); ?>
                              </div>
                            </div>

                            <div class="row">
                              <div class="col-md-12">
                                    <?php echo render_textarea('long_description', 'description'); ?>
                              </div>
                            </div>


                            <div class="row">
                              
                                <div class="col-md-6">
                                     <?php echo render_select('group_id',$commodity_groups,array('id','name'),'commodity_group'); ?>
                                </div>
                                 <div class="col-md-6">
                                     <?php echo render_select('sub_group',$sub_groups,array('id','sub_group_name'),'sub_group'); ?>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">

                                     <?php $premium_rates = isset($premium_rates) ? $premium_rates : '' ?>
                                    <?php
                                    $attr = array();
                                    //$attr = ['data-type' => 'currency'];
                                     echo render_input('rate', 'rate','', 'number', $attr); ?>
                                </div>
                                <div class="col-md-6">
                                    <?php 
                                    $attr = array();
                                    //$attr = ['data-type' => 'currency'];
                                     echo render_input('purchase_price', 'purchase_price','', 'number', $attr); ?>
                                  
                                </div>
                            </div>
                            <div class="row">
                              <div class="col-md-6">
                                     <?php echo render_select('unit_id',$units,array('unit_type_id','unit_name'),'units'); ?>
                                </div>
                                
                              <div class="col-md-3">
                                     <?php echo render_select('tax',$taxes,array('id','label'),'tax_1'); ?>
                              </div>

                              <div class="col-md-3">
                                     <?php echo render_select('tax2',$taxes,array('id','label'),'tax_2'); ?>
                              </div>

                            </div>

                            <div id="custom_fields_items">
                              <?php echo render_custom_fields('items'); ?>
                            </div>

                            <?php if(!isset($expense) || (isset($expense) && $expense->attachment == '')){ ?>
                            <div id="dropzoneDragArea" class="dz-default dz-message">
                               <span><?php echo _l('item_add_edit_attach_image'); ?></span>
                            </div>
                            <div class="dropzone-previews"></div>
                            <?php } ?>

                            <div id="images_old_preview">
                              
                            </div>

                        
                  </div>

            </div>

            <div class="modal-footer">
              <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                <button type="submit" class="btn btn-info"><?php echo _l('submit'); ?></button>
            </div>
          </div>

          </div>
        </div><!-- /.modal-content -->
            <?php echo form_close(); ?>

<!-- add one commodity list sibar end -->   

<?php init_tail(); ?>
</body>
</html>
<?php require 'modules/purchase/assets/js/item_list_js.php';?>
