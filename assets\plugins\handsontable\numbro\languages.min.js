!function(n){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=n();else if("function"==typeof define&&define.amd)define([],n);else{var t;((t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).allLanguages=n()}}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof require&&require;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof require&&require,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).bg=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"bg",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"И",million:"А",billion:"M",trillion:"T"},ordinal:function(){return"."},currency:{symbol:"лв.",code:"BGN"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).csCZ=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"cs-CZ",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tis.",million:"mil.",billion:"mld.",trillion:"bil."},ordinal:function(){return"."},spaceSeparated:!0,currency:{symbol:"Kč",position:"postfix",code:"CZK"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],3:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).daDK=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"da-DK",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"t",million:"mio",billion:"mia",trillion:"b"},ordinal:function(){return"."},currency:{symbol:"kr",position:"postfix",code:"DKK"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],4:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).deAT=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"de-AT",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"€",code:"EUR"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],5:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).deCH=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"de-CH",delimiters:{thousands:"'",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"CHF",position:"postfix",code:"CHF"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],6:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).deDE=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"de-DE",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(){return"."},spaceSeparated:!0,currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{totalLength:4,thousandSeparated:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],7:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).deLI=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"de-LI",delimiters:{thousands:"'",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"CHF",position:"postfix",code:"CHF"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],8:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).el=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"el",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"χ",million:"ε",billion:"δ",trillion:"τ"},ordinal:function(){return"."},currency:{symbol:"€",code:"EUR"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],9:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).enAU=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"en-AU",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1==~~(n%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th"},currency:{symbol:"$",position:"prefix",code:"AUD"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{thousandSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],10:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).enGB=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"en-GB",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1==~~(n%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th"},currency:{symbol:"£",position:"prefix",code:"GBP"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",thousandSeparated:!0,spaceSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,spaceSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],11:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).enIE=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"en-IE",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1==~~(n%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th"},currency:{symbol:"€",code:"EUR"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],12:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).enNZ=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"en-NZ",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1==~~(n%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th"},currency:{symbol:"$",position:"prefix",code:"NZD"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{thousandSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],13:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).enZA=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"en-ZA",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1==~~(n%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th"},currency:{symbol:"R",position:"prefix",code:"ZAR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{thousandSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],14:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).esAR=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"es-AR",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1===t||3===t?"er":2===t?"do":7===t||0===t?"mo":8===t?"vo":9===t?"no":"to"},currency:{symbol:"$",position:"postfix",code:"ARS"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],15:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).esCL=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"es-CL",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1===t||3===t?"er":2===t?"do":7===t||0===t?"mo":8===t?"vo":9===t?"no":"to"},currency:{symbol:"$",position:"prefix",code:"CLP"},currencyFormat:{output:"currency",thousandSeparated:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],16:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).esCO=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"es-CO",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1===t||3===t?"er":2===t?"do":7===t||0===t?"mo":8===t?"vo":9===t?"no":"to"},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],17:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).esCR=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"es-CR",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1===t||3===t?"er":2===t?"do":7===t||0===t?"mo":8===t?"vo":9===t?"no":"to"},currency:{symbol:"₡",position:"postfix",code:"CRC"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],18:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).esES=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"es-ES",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1===t||3===t?"er":2===t?"do":7===t||0===t?"mo":8===t?"vo":9===t?"no":"to"},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],19:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).esMX=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"es-MX",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1===t||3===t?"er":2===t?"do":7===t||0===t?"mo":8===t?"vo":9===t?"no":"to"},currency:{symbol:"$",position:"postfix",code:"MXN"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],20:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).esNI=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"es-NI",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1===t||3===t?"er":2===t?"do":7===t||0===t?"mo":8===t?"vo":9===t?"no":"to"},currency:{symbol:"C$",position:"prefix",code:"NIO"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],21:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).esPE=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"es-PE",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1===t||3===t?"er":2===t?"do":7===t||0===t?"mo":8===t?"vo":9===t?"no":"to"},currency:{symbol:"S/.",position:"prefix",code:"PEN"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],22:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).esPR=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"es-PR",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1===t||3===t?"er":2===t?"do":7===t||0===t?"mo":8===t?"vo":9===t?"no":"to"},currency:{symbol:"$",position:"prefix",code:"USD"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],23:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).esSV=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"es-SV",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1===t||3===t?"er":2===t?"do":7===t||0===t?"mo":8===t?"vo":9===t?"no":"to"},currency:{symbol:"$",position:"prefix",code:"SVC"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],24:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).etEE=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"et-EE",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tuh",million:"mln",billion:"mld",trillion:"trl"},ordinal:function(){return"."},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],25:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).faIR=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"fa-IR",delimiters:{thousands:"،",decimal:"."},abbreviations:{thousand:"هزار",million:"میلیون",billion:"میلیارد",trillion:"تریلیون"},ordinal:function(){return"ام"},currency:{symbol:"﷼",code:"IRR"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],26:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).fiFI=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"fi-FI",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"M",billion:"G",trillion:"T"},ordinal:function(){return"."},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],27:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).filPH=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"fil-PH",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(n){var t=n%10;return 1==~~(n%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th"},currency:{symbol:"₱",code:"PHP"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],28:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).frCA=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"fr-CA",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"M",billion:"G",trillion:"T"},ordinal:function(n){return 1===n?"er":"ème"},spaceSeparated:!0,currency:{symbol:"$",position:"postfix",code:"USD"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{thousandSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],29:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).frCH=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"fr-CH",delimiters:{thousands:" ",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(n){return 1===n?"er":"ème"},currency:{symbol:"CHF",position:"postfix",code:"CHF"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],30:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).frFR=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"fr-FR",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(n){return 1===n?"er":"ème"},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],31:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).heIL=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"he-IL",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"אלף",million:"מליון",billion:"בליון",trillion:"טריליון"},currency:{symbol:"₪",position:"prefix",code:"ILS"},ordinal:function(){return""},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],32:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).huHU=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"hu-HU",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"E",million:"M",billion:"Mrd",trillion:"T"},ordinal:function(){return"."},currency:{symbol:"Ft",position:"postfix",code:"HUF"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],33:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).id=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"id",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"r",million:"j",billion:"m",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"Rp",code:"IDR"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],34:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).itCH=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"it-CH",delimiters:{thousands:"'",decimal:"."},abbreviations:{thousand:"mila",million:"mil",billion:"b",trillion:"t"},ordinal:function(){return"°"},currency:{symbol:"CHF",code:"CHF"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],35:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).itIT=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"it-IT",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"mila",million:"mil",billion:"b",trillion:"t"},ordinal:function(){return"º"},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],36:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).jaJP=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"ja-JP",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百万",billion:"十億",trillion:"兆"},ordinal:function(){return"."},currency:{symbol:"¥",position:"prefix",code:"JPY"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{thousandSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],37:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).koKR=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"ko-KR",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"천",million:"백만",billion:"십억",trillion:"일조"},ordinal:function(){return"."},currency:{symbol:"₩",code:"KPW"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],38:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).lvLV=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"lv-LV",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tūkst.",million:"milj.",billion:"mljrd.",trillion:"trilj."},ordinal:function(){return"."},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],39:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).nbNO=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"nb-NO",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"t",million:"M",billion:"md",trillion:"b"},ordinal:function(){return""},currency:{symbol:"kr",position:"postfix",code:"NOK"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],40:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).nb=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"nb",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"t",million:"mil",billion:"mia",trillion:"b"},ordinal:function(){return"."},currency:{symbol:"kr",code:"NOK"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],41:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).nlBE=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"nl-BE",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"mln",billion:"mld",trillion:"bln"},ordinal:function(n){var t=n%100;return 0!==n&&t<=1||8===t||20<=t?"ste":"de"},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],42:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).nlNL=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"nl-NL",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mln",billion:"mrd",trillion:"bln"},ordinal:function(n){var t=n%100;return 0!==n&&t<=1||8===t||20<=t?"ste":"de"},currency:{symbol:"€",position:"prefix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],43:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).nn=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"nn",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"t",million:"mil",billion:"mia",trillion:"b"},ordinal:function(){return"."},currency:{symbol:"kr",code:"NOK"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],44:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).plPL=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"pl-PL",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tys.",million:"mln",billion:"mld",trillion:"bln"},ordinal:function(){return"."},currency:{symbol:" zł",position:"postfix",code:"PLN"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],45:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).ptBR=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"pt-BR",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"mil",million:"milhões",billion:"b",trillion:"t"},ordinal:function(){return"º"},currency:{symbol:"R$",position:"prefix",code:"BRL"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],46:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).ptPT=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"pt-PT",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(){return"º"},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],47:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).roRO=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"ro-RO",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"mii",million:"mil",billion:"mld",trillion:"bln"},ordinal:function(){return"."},currency:{symbol:" lei",position:"postfix",code:"RON"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],48:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).ro=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"ro-RO",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"mii",million:"mil",billion:"mld",trillion:"bln"},ordinal:function(){return"."},currency:{symbol:" lei",position:"postfix",code:"RON"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}],2:[function(n,t,e){t.exports=n("./ro-RO")},{"./ro-RO":1}]},{},[2])(2)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],49:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).ruRU=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"ru-RU",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"тыс.",million:"млн",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"руб.",position:"postfix",code:"RUB"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],50:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).ruUA=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"ru-UA",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"тыс.",million:"млн",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"₴",position:"postfix",code:"UAH"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],51:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).skSK=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"sk-SK",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tis.",million:"mil.",billion:"mld.",trillion:"bil."},ordinal:function(){return"."},spaceSeparated:!0,currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],52:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).sl=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"sl",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"tis.",million:"mil.",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"€",code:"EUR"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],53:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).srCyrlRS=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"sr-Cyrl-RS",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"тыс.",million:"млн",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"RSD",code:"RSD"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],54:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).svSE=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"sv-SE",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"t",million:"M",billion:"md",trillion:"tmd"},ordinal:function(){return""},currency:{symbol:"kr",position:"postfix",code:"SEK"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],55:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).thTH=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"th-TH",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"พัน",million:"ล้าน",billion:"พันล้าน",trillion:"ล้านล้าน"},ordinal:function(){return"."},currency:{symbol:"฿",position:"postfix",code:"THB"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],56:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).trTR=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){var o={1:"'inci",5:"'inci",8:"'inci",70:"'inci",80:"'inci",2:"'nci",7:"'nci",20:"'nci",50:"'nci",3:"'üncü",4:"'üncü",100:"'üncü",6:"'ncı",9:"'uncu",10:"'uncu",30:"'uncu",60:"'ıncı",90:"'ıncı"};t.exports={languageTag:"tr-TR",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"bin",million:"milyon",billion:"milyar",trillion:"trilyon"},ordinal:function(n){if(0===n)return"'ıncı";var t=n%10;return o[t]||o[n%100-t]||o[100<=n?100:null]},currency:{symbol:"₺",position:"postfix",code:"TRY"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],57:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).ukUA=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"uk-UA",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"тис.",million:"млн",billion:"млрд",trillion:"блн"},ordinal:function(){return""},currency:{symbol:"₴",position:"postfix",code:"UAH"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],58:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).zhCN=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"zh-CN",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百万",billion:"十亿",trillion:"兆"},ordinal:function(){return"."},currency:{symbol:"¥",position:"prefix",code:"CNY"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{thousandSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],59:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).zhMO=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"zh-MO",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百萬",billion:"十億",trillion:"兆"},ordinal:function(){return"."},currency:{symbol:"MOP",code:"MOP"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],60:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).zhSG=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"zh-SG",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百万",billion:"十亿",trillion:"兆"},ordinal:function(){return"."},currency:{symbol:"$",code:"SGD"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],61:[function(d,r,i){(function(e){"use strict";function o(n){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}!function(n){var t;"object"==(void 0===i?"undefined":o(i))&&void 0!==r?r.exports=n():((t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:this).numbro||(t.numbro={})).zhTW=n()}(function(){return function i(u,a,f){function s(t,n){if(!a[t]){if(!u[t]){var e="function"==typeof d&&d;if(!n&&e)return e(t,!0);if(l)return l(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=a[t]={exports:{}};u[t][0].call(r.exports,function(n){return s(u[t][1][n]||n)},r,r.exports,i,u,a,f)}return a[t].exports}for(var l="function"==typeof d&&d,n=0;n<f.length;n++)s(f[n]);return s}({1:[function(n,t,e){t.exports={languageTag:"zh-TW",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百萬",billion:"十億",trillion:"兆"},ordinal:function(){return"第"},currency:{symbol:"NT$",code:"TWD"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],62:[function(n,t,e){"use strict";e.bg=n("./dist/languages/bg.min.js"),e["cs-CZ"]=n("./dist/languages/cs-CZ.min.js"),e["da-DK"]=n("./dist/languages/da-DK.min.js"),e["de-AT"]=n("./dist/languages/de-AT.min.js"),e["de-CH"]=n("./dist/languages/de-CH.min.js"),e["de-DE"]=n("./dist/languages/de-DE.min.js"),e["de-LI"]=n("./dist/languages/de-LI.min.js"),e.el=n("./dist/languages/el.min.js"),e["en-AU"]=n("./dist/languages/en-AU.min.js"),e["en-GB"]=n("./dist/languages/en-GB.min.js"),e["en-IE"]=n("./dist/languages/en-IE.min.js"),e["en-NZ"]=n("./dist/languages/en-NZ.min.js"),e["en-ZA"]=n("./dist/languages/en-ZA.min.js"),e["es-AR"]=n("./dist/languages/es-AR.min.js"),e["es-CL"]=n("./dist/languages/es-CL.min.js"),e["es-CO"]=n("./dist/languages/es-CO.min.js"),e["es-CR"]=n("./dist/languages/es-CR.min.js"),e["es-ES"]=n("./dist/languages/es-ES.min.js"),e["es-MX"]=n("./dist/languages/es-MX.min.js"),e["es-NI"]=n("./dist/languages/es-NI.min.js"),e["es-PE"]=n("./dist/languages/es-PE.min.js"),e["es-PR"]=n("./dist/languages/es-PR.min.js"),e["es-SV"]=n("./dist/languages/es-SV.min.js"),e["et-EE"]=n("./dist/languages/et-EE.min.js"),e["fa-IR"]=n("./dist/languages/fa-IR.min.js"),e["fi-FI"]=n("./dist/languages/fi-FI.min.js"),e["fil-PH"]=n("./dist/languages/fil-PH.min.js"),e["fr-CA"]=n("./dist/languages/fr-CA.min.js"),e["fr-CH"]=n("./dist/languages/fr-CH.min.js"),e["fr-FR"]=n("./dist/languages/fr-FR.min.js"),e["he-IL"]=n("./dist/languages/he-IL.min.js"),e["hu-HU"]=n("./dist/languages/hu-HU.min.js"),e.id=n("./dist/languages/id.min.js"),e["it-CH"]=n("./dist/languages/it-CH.min.js"),e["it-IT"]=n("./dist/languages/it-IT.min.js"),e["ja-JP"]=n("./dist/languages/ja-JP.min.js"),e["ko-KR"]=n("./dist/languages/ko-KR.min.js"),e["lv-LV"]=n("./dist/languages/lv-LV.min.js"),e["nb-NO"]=n("./dist/languages/nb-NO.min.js"),e.nb=n("./dist/languages/nb.min.js"),e["nl-BE"]=n("./dist/languages/nl-BE.min.js"),e["nl-NL"]=n("./dist/languages/nl-NL.min.js"),e.nn=n("./dist/languages/nn.min.js"),e["pl-PL"]=n("./dist/languages/pl-PL.min.js"),e["pt-BR"]=n("./dist/languages/pt-BR.min.js"),e["pt-PT"]=n("./dist/languages/pt-PT.min.js"),e["ro-RO"]=n("./dist/languages/ro-RO.min.js"),e.ro=n("./dist/languages/ro.min.js"),e["ru-RU"]=n("./dist/languages/ru-RU.min.js"),e["ru-UA"]=n("./dist/languages/ru-UA.min.js"),e["sk-SK"]=n("./dist/languages/sk-SK.min.js"),e.sl=n("./dist/languages/sl.min.js"),e["sr-Cyrl-RS"]=n("./dist/languages/sr-Cyrl-RS.min.js"),e["sv-SE"]=n("./dist/languages/sv-SE.min.js"),e["th-TH"]=n("./dist/languages/th-TH.min.js"),e["tr-TR"]=n("./dist/languages/tr-TR.min.js"),e["uk-UA"]=n("./dist/languages/uk-UA.min.js"),e["zh-CN"]=n("./dist/languages/zh-CN.min.js"),e["zh-MO"]=n("./dist/languages/zh-MO.min.js"),e["zh-SG"]=n("./dist/languages/zh-SG.min.js"),e["zh-TW"]=n("./dist/languages/zh-TW.min.js")},{"./dist/languages/bg.min.js":1,"./dist/languages/cs-CZ.min.js":2,"./dist/languages/da-DK.min.js":3,"./dist/languages/de-AT.min.js":4,"./dist/languages/de-CH.min.js":5,"./dist/languages/de-DE.min.js":6,"./dist/languages/de-LI.min.js":7,"./dist/languages/el.min.js":8,"./dist/languages/en-AU.min.js":9,"./dist/languages/en-GB.min.js":10,"./dist/languages/en-IE.min.js":11,"./dist/languages/en-NZ.min.js":12,"./dist/languages/en-ZA.min.js":13,"./dist/languages/es-AR.min.js":14,"./dist/languages/es-CL.min.js":15,"./dist/languages/es-CO.min.js":16,"./dist/languages/es-CR.min.js":17,"./dist/languages/es-ES.min.js":18,"./dist/languages/es-MX.min.js":19,"./dist/languages/es-NI.min.js":20,"./dist/languages/es-PE.min.js":21,"./dist/languages/es-PR.min.js":22,"./dist/languages/es-SV.min.js":23,"./dist/languages/et-EE.min.js":24,"./dist/languages/fa-IR.min.js":25,"./dist/languages/fi-FI.min.js":26,"./dist/languages/fil-PH.min.js":27,"./dist/languages/fr-CA.min.js":28,"./dist/languages/fr-CH.min.js":29,"./dist/languages/fr-FR.min.js":30,"./dist/languages/he-IL.min.js":31,"./dist/languages/hu-HU.min.js":32,"./dist/languages/id.min.js":33,"./dist/languages/it-CH.min.js":34,"./dist/languages/it-IT.min.js":35,"./dist/languages/ja-JP.min.js":36,"./dist/languages/ko-KR.min.js":37,"./dist/languages/lv-LV.min.js":38,"./dist/languages/nb-NO.min.js":39,"./dist/languages/nb.min.js":40,"./dist/languages/nl-BE.min.js":41,"./dist/languages/nl-NL.min.js":42,"./dist/languages/nn.min.js":43,"./dist/languages/pl-PL.min.js":44,"./dist/languages/pt-BR.min.js":45,"./dist/languages/pt-PT.min.js":46,"./dist/languages/ro-RO.min.js":47,"./dist/languages/ro.min.js":48,"./dist/languages/ru-RU.min.js":49,"./dist/languages/ru-UA.min.js":50,"./dist/languages/sk-SK.min.js":51,"./dist/languages/sl.min.js":52,"./dist/languages/sr-Cyrl-RS.min.js":53,"./dist/languages/sv-SE.min.js":54,"./dist/languages/th-TH.min.js":55,"./dist/languages/tr-TR.min.js":56,"./dist/languages/uk-UA.min.js":57,"./dist/languages/zh-CN.min.js":58,"./dist/languages/zh-MO.min.js":59,"./dist/languages/zh-SG.min.js":60,"./dist/languages/zh-TW.min.js":61}]},{},[62])(62)});
//# sourceMappingURL=languages.min.js.map
