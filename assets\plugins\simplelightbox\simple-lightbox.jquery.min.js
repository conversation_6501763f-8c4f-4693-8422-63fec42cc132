/*!
	By <PERSON>, www.andrerinas.de
	Documentation, www.simplelightbox.de
	Available for use under the MIT License
	Version 2.2.1
*/
"use strict";function _createForOfIteratorHelper(t){if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(t=_unsupportedIterableToArray(t))){var e=0,i=function(){};return{s:i,n:function(){return e>=t.length?{done:!0}:{done:!1,value:t[e++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,s,o=!0,a=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return o=t.done,t},e:function(t){a=!0,s=t},f:function(){try{o||null==n.return||n.return()}finally{if(a)throw s}}}}function _unsupportedIterableToArray(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(i):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(t,e):void 0}}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function _createClass(t,e,i){return e&&_defineProperties(t.prototype,e),i&&_defineProperties(t,i),t}function _defineProperty(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var SimpleLightbox=function(){function n(t,e){var i=this;_classCallCheck(this,n),_defineProperty(this,"a",{sourceAttr:"href",overlay:!0,spinner:!0,nav:!0,navText:["&lsaquo;","&rsaquo;"],captions:!0,captionDelay:0,captionSelector:"img",captionType:"attr",captionsData:"title",captionPosition:"bottom",captionClass:"",close:!0,closeText:"&times;",swipeClose:!0,showCounter:!0,fileExt:"png|jpg|jpeg|gif|webp",animationSlide:!0,animationSpeed:250,preloading:!0,enableKeyboard:!0,loop:!0,rel:!1,docClose:!0,swipeTolerance:50,className:"simple-lightbox",widthRatio:.8,heightRatio:.9,scaleImageToRatio:!1,disableRightClick:!1,disableScroll:!0,alertError:!0,alertErrorMessage:"Image not found, next image will be loaded",additionalHtml:!1,history:!0,throttleInterval:0,doubleTapZoom:2,maxZoom:10,htmlClass:"has-lightbox",rtl:!1}),_defineProperty(this,"b",void 0),_defineProperty(this,"c",!1),_defineProperty(this,"d","ontouchstart"in window),_defineProperty(this,"e",void 0),_defineProperty(this,"f","pushState"in history),_defineProperty(this,"g",!1),_defineProperty(this,"h",!1),_defineProperty(this,"i",!1),_defineProperty(this,"j",!1),_defineProperty(this,"k",!1),_defineProperty(this,"l",!1),_defineProperty(this,"m",null),_defineProperty(this,"n",void 0),_defineProperty(this,"o","simplelightbox"),_defineProperty(this,"p",{}),_defineProperty(this,"q",[]),_defineProperty(this,"r",0),_defineProperty(this,"s",0),_defineProperty(this,"t",null),_defineProperty(this,"u",0),_defineProperty(this,"v",{a:0,b:0,c:0,d:0,e:0,f:0,g:!1,h:0,i:!1,j:0,k:0,l:0,m:0,n:0,o:0,p:!1,q:0,r:0,s:0,t:0,u:0,v:0,w:1,x:0,y:0,z:0,A:0,B:0,C:0,D:0,E:0,F:0,G:0,H:0,I:0,J:0,K:0,L:0,M:!1,N:0}),this.options=Object.assign(this.a,e),"string"==typeof t?(this.t=t,this.elements=Array.from(document.querySelectorAll(t))):this.elements=void 0!==t.length&&0<t.length?Array.from(t):[t],this.relatedElements=[],this.b=this.calculateTransitionPrefix(),this.c=!1!==this.b,this.e=this.hash,this.options.rel&&(this.elements=this.getRelated(this.options.rel)),this.createDomNodes(),this.options.close&&this.p.a.appendChild(this.p.b),this.options.nav&&this.p.a.appendChild(this.p.c),this.options.spinner&&this.p.a.appendChild(this.p.d),this.addEventListener(this.elements,"click."+this.o,function(t){if(i.isValidLink(t.currentTarget)){if(t.preventDefault(),i.h)return!1;i.r=i.elements.indexOf(t.currentTarget),i.openImage(t.currentTarget)}}),this.options.docClose&&this.addEventListener(this.p.e,["click."+this.o,"touchstart."+this.o],function(t){i.g&&i.close()}),this.options.disableRightClick&&this.addEventListener(document.body,"contextmenu."+this.o,function(t){t.target.classList.contains("sl-overlay")&&t.preventDefault()}),this.options.enableKeyboard&&this.addEventListener(document.body,"keyup."+this.o,this.throttle(function(t){if(i.v.a=0,i.h&&"Escape"===t.key)return i.n.setAttribute("src",""),i.h=!1,i.close();i.g&&(t.preventDefault(),"Escape"===t.key&&i.close(),!i.h&&-1<["ArrowLeft","ArrowRight"].indexOf(t.key)&&i.loadImage("ArrowRight"===t.key?1:-1))},this.options.throttleInterval)),this.addEvents()}return _createClass(n,[{key:"createDomNodes",value:function(){this.p.e=document.createElement("div"),this.p.e.classList.add("sl-overlay"),this.p.e.dataset.opacityTarget=".7",this.p.b=document.createElement("button"),this.p.b.classList.add("sl-close"),this.p.b.innerHTML=this.options.closeText,this.p.d=document.createElement("div"),this.p.d.classList.add("sl-spinner"),this.p.d.innerHTML="<div></div>",this.p.c=document.createElement("div"),this.p.c.classList.add("sl-navigation"),this.p.c.innerHTML='<button class="sl-prev">'.concat(this.options.navText[0],'</button><button class="sl-next">').concat(this.options.navText[1],"</button>"),this.p.f=document.createElement("div"),this.p.f.classList.add("sl-counter"),this.p.f.innerHTML='<span class="sl-current"></span>/<span class="sl-total"></span>',this.p.g=document.createElement("div"),this.p.g.classList.add("sl-caption","pos-"+this.options.captionPosition),this.options.captionClass&&this.p.g.classList.add(this.options.captionClass),this.p.h=document.createElement("div"),this.p.h.classList.add("sl-image"),this.p.a=document.createElement("div"),this.p.a.classList.add("sl-wrapper"),this.options.className&&this.p.a.classList.add(this.options.className),this.options.rtl&&this.p.a.classList.add("sl-dir-rtl")}},{key:"throttle",value:function(t,e){var i;return function(){i||(t.apply(this,arguments),i=!0,setTimeout(function(){return i=!1},e))}}},{key:"isValidLink",value:function(t){return!this.options.fileExt||"pathname"in t&&new RegExp("("+this.options.fileExt+")$","i").test(t.pathname)}},{key:"calculateTransitionPrefix",value:function(){var t=(document.body||document.documentElement).style;return"transition"in t?"":"WebkitTransition"in t?"-webkit-":"MozTransition"in t?"-moz-":"OTransition"in t&&"-o"}},{key:"toggleScrollbar",value:function(t){var e=0;if("hide"===t){var i=window.innerWidth;if(!i){var n=document.documentElement.getBoundingClientRect();i=n.right-Math.abs(n.left)}if(document.body.clientWidth<i){var s=document.createElement("div"),o=parseInt(document.body.style.paddingRight||0,10);s.classList.add("sl-scrollbar-measure"),document.body.appendChild(s),e=s.offsetWidth-s.clientWidth,document.body.removeChild(s),document.body.dataset.originalPaddingRight=o,0<e&&(document.body.classList.add("hidden-scroll"),document.body.style.paddingRight=o+e+"px")}}else document.body.classList.remove("hidden-scroll"),document.body.style.paddingRight=document.body.dataset.originalPaddingRight;return e}},{key:"close",value:function(){var t=this;if(!this.g||this.h||this.i)return!1;this.i=!0;var e=this.relatedElements[this.nIndex];for(var i in e.dispatchEvent(new Event("close.simplelightbox")),this.options.history&&(this.l=!1,this.k||this.resetHash()),this.fadeOut(document.querySelectorAll(".sl-image img, .sl-overlay, .sl-close, .sl-navigation, .sl-image .sl-caption, .sl-counter"),300,function(){t.options.disableScroll&&t.toggleScrollbar("show"),t.options.htmlClass&&""!==t.options.htmlClass&&document.querySelector("html").classList.remove(t.options.htmlClass),document.body.removeChild(t.p.a),document.body.removeChild(t.p.e),t.p.i=null,e.dispatchEvent(new Event("closed.simplelightbox")),t.i=!1}),this.n=null,this.g=!1,this.h=!1,this.v)this.v[i]=0;this.v.g=!1,this.v.i=!1,this.v.p=!1,this.v.w=this.minMax(1,1,this.options.maxZoom),this.v.M=!1}},{key:"preload",value:function(){var i=this,n=this.nIndex,t=this.relatedElements.length,e=n+1<0?t-1:t-1<=n+1?0:n+1,s=n-1<0?t-1:t-1<=n-1?0:n-1,o=new Image,a=new Image;o.addEventListener("load",function(t){var e=t.target.getAttribute("src");-1===i.q.indexOf(e)&&i.q.push(e),i.relatedElements[n].dispatchEvent(new Event("nextImageLoaded."+i.o))}),o.setAttribute("src",this.relatedElements[e].getAttribute(this.options.sourceAttr)),a.addEventListener("load",function(t){var e=t.target.getAttribute("src");-1===i.q.indexOf(e)&&i.q.push(e),i.relatedElements[n].dispatchEvent(new Event("prevImageLoaded."+i.o))}),a.setAttribute("src",this.relatedElements[s].getAttribute(this.options.sourceAttr))}},{key:"loadImage",value:function(t){var e=this,i=t;this.options.rtl&&(t=-t),this.relatedElements[this.nIndex].dispatchEvent(new Event("change."+this.o)),this.relatedElements[this.nIndex].dispatchEvent(new Event((1===t?"next":"prev")+"."+this.o));var n=this.nIndex+t;if(this.h||(n<0||n>=this.relatedElements.length)&&!1===this.options.loop)return!1;this.nIndex=n<0?this.relatedElements.length-1:n>this.relatedElements.length-1?0:n,this.p.f.querySelector(".sl-current").innerHTML=this.nIndex+1,this.options.animationSlide&&this.slide(this.options.animationSpeed/1e3,-100*i-this.v.a+"px"),this.fadeOut(this.p.h,300,function(){e.h=!0,setTimeout(function(){var t=e.relatedElements[e.nIndex];e.n.setAttribute("src",t.getAttribute(e.options.sourceAttr)),-1===e.q.indexOf(t.getAttribute(e.options.sourceAttr))&&e.show(e.p.d),e.p.h.contains(e.p.g)&&e.p.h.removeChild(e.p.g),e.adjustImage(i),e.options.preloading&&e.preload()},100)})}},{key:"adjustImage",value:function(a){var r=this;if(!this.n)return!1;var t=new Image,l=window.innerWidth*this.options.widthRatio,h=window.innerHeight*this.options.heightRatio;t.setAttribute("src",this.n.getAttribute("src")),this.n.dataset.scale=1,this.n.dataset.translateX=0,this.n.dataset.translateY=0,this.zoomPanElement(0,0,1),t.addEventListener("error",function(t){r.relatedElements[r.nIndex].dispatchEvent(new Event("error."+r.o)),r.h=!1,r.g=!1,r.p.d.style.display="none";var e=1===a||-1===a;if(r.r===r.nIndex&&e)return r.close();r.options.alertError&&alert(r.options.alertErrorMessage),r.loadImage(e?a:1)}),t.addEventListener("load",function(t){void 0!==a&&(r.relatedElements[r.nIndex].dispatchEvent(new Event("changed."+r.o)),r.relatedElements[r.nIndex].dispatchEvent(new Event((1===a?"nextDone":"prevDone")+"."+r.o))),r.options.history&&r.updateURL(),-1===r.q.indexOf(r.n.getAttribute("src"))&&r.q.push(r.n.getAttribute("src"));var e=t.target.width,i=t.target.height;if(r.options.scaleImageToRatio||l<e||h<i){var n=l/h<e/i?e/l:i/h;e/=n,i/=n}r.p.h.style.top=(window.innerHeight-i)/2+"px",r.p.h.style.left=(window.innerWidth-e-r.u)/2+"px",r.p.h.style.width=e+"px",r.p.h.style.height=i+"px",r.p.d.style.display="none",r.fadeIn(r.n,300),r.g=!0;var s,o="self"===r.options.captionSelector?r.relatedElements[r.nIndex]:r.relatedElements[r.nIndex].querySelector(r.options.captionSelector);r.options.captions&&o&&(s="data"===r.options.captionType?o.dataset[r.options.captionsData]:"text"===r.options.captionType?o.innerHTML:o.getAttribute(r.options.captionsData)),r.options.loop||(0===r.nIndex&&r.hide(r.p.c.querySelector(".sl-prev")),r.nIndex>=r.relatedElements.length-1&&r.hide(r.p.c.querySelector(".sl-next")),0<r.nIndex&&r.show(r.p.c.querySelector(".sl-prev")),r.nIndex<r.relatedElements.length-1&&r.show(r.p.c.querySelector(".sl-next"))),1===r.relatedElements.length?r.hide(r.p.c.querySelectorAll(".sl-prev, .sl-next")):r.show(r.p.c.querySelectorAll(".sl-prev, .sl-next")),1===a||-1===a?(r.options.animationSlide&&(r.slide(0,100*a+"px"),setTimeout(function(){r.slide(r.options.animationSpeed/1e3,"0px")},50)),r.fadeIn(r.p.h,300,function(){r.h=!1,r.setCaption(s,e)})):(r.h=!1,r.setCaption(s,e)),r.options.additionalHtml&&!r.p.i&&(r.p.i=document.createElement("div"),r.p.i.classList.add("sl-additional-html"),r.p.i.innerHTML=r.options.additionalHtml,r.p.h.appendChild(r.p.i))})}},{key:"zoomPanElement",value:function(t,e,i){this.n.style[this.b+"transform"]="translate("+t+","+e+") scale("+i+")"}},{key:"minMax",value:function(t,e,i){return t<e?e:i<t?i:t}},{key:"setZoomData",value:function(t,e,i){this.n.dataset.scale=t,this.n.dataset.translateX=e,this.n.dataset.translateY=i}},{key:"hashchangeHandler",value:function(){this.g&&this.hash===this.e&&(this.k=!0,this.close())}},{key:"addEvents",value:function(){var i=this;this.addEventListener(window,"resize."+this.o,function(t){i.g&&i.adjustImage()}),this.addEventListener(this.p.b,["click."+this.o,"touchstart."+this.o],this.close.bind(this)),this.options.history&&setTimeout(function(){i.addEventListener(window,"hashchange."+i.o,function(t){i.g&&i.hashchangeHandler()})},40),this.addEventListener(this.p.c.getElementsByTagName("button"),"click."+this.o,function(t){if(!t.currentTarget.tagName.match(/button/i))return!0;t.preventDefault(),i.v.a=0,i.loadImage(t.currentTarget.classList.contains("sl-next")?1:-1)}),this.addEventListener(this.p.h,["touchstart."+this.o,"mousedown."+this.o],function(t){if("A"===t.target.tagName&&"touchstart"===t.type)return!0;if("mousedown"===t.type)i.v.s=t.clientX,i.v.t=t.clientY,i.v.j=i.getDimensions(i.p.h).height,i.v.k=i.getDimensions(i.p.h).width,i.v.n=i.getDimensions(i.n).height,i.v.o=i.getDimensions(i.n).width,i.v.l=i.p.h.offsetLeft,i.v.m=i.p.h.offsetTop,i.v.q=parseFloat(i.n.dataset.translateX),i.v.r=parseFloat(i.n.dataset.translateY),i.v.p=!0;else{if(i.v.L=t.touches.length,i.v.s=t.touches[0].clientX,i.v.t=t.touches[0].clientY,i.v.j=i.getDimensions(i.p.h).height,i.v.k=i.getDimensions(i.p.h).width,i.v.n=i.getDimensions(i.n).height,i.v.o=i.getDimensions(i.n).width,i.v.l=i.p.h.offsetLeft,i.v.m=i.p.h.offsetTop,1===i.v.L){if(i.v.M)return i.n.classList.add("sl-transition"),i.v.i?(i.v.w=1,i.setZoomData(i.v.w,0,0),i.zoomPanElement("0px","0px",i.v.w),i.v.i=!1):(i.v.w=i.options.doubleTapZoom,i.setZoomData(i.v.w,0,0),i.zoomPanElement("0px","0px",i.v.w),i.p.g.style.opacity||"none"===i.p.g.style.display||i.fadeOut(i.p.g,200),i.v.i=!0),setTimeout(function(){i.n&&i.n.classList.remove("sl-transition")},200),!1;i.v.M=!0,setTimeout(function(){i.v.M=!1},300),i.v.q=parseFloat(i.n.dataset.translateX),i.v.r=parseFloat(i.n.dataset.translateY)}else 2===i.v.L&&(i.v.s2=t.touches[1].clientX,i.v.t2=t.touches[1].clientY,i.v.q=parseFloat(i.n.dataset.translateX),i.v.r=parseFloat(i.n.dataset.translateY),i.v.F=(i.v.s+i.v.s2)/2,i.v.G=(i.v.t+i.v.t2)/2,i.v.x=Math.sqrt((i.v.s-i.v.s2)*(i.v.s-i.v.s2)+(i.v.t-i.v.t2)*(i.v.t-i.v.t2)));i.v.p=!0}return!!i.v.g||(i.c&&(i.v.h=parseInt(i.p.h.style.left,10)),i.v.g=!0,i.v.a=0,i.v.b=0,i.v.c=t.pageX||t.touches[0].pageX,i.v.e=t.pageY||t.touches[0].pageY,!1)}),this.addEventListener(this.p.h,["touchmove."+this.o,"mousemove."+this.o,"MSPointerMove"],function(t){if(!i.v.g)return!0;if(t.preventDefault(),"touchmove"===t.type){if(!1===i.v.p)return!1;i.v.y=t.touches[0].clientX,i.v.z=t.touches[0].clientY,i.v.L=t.touches.length,i.v.N++,1<i.v.L?(i.v.y2=t.touches[1].clientX,i.v.z2=t.touches[1].clientY,i.v.K=Math.sqrt((i.v.y-i.v.y2)*(i.v.y-i.v.y2)+(i.v.z-i.v.z2)*(i.v.z-i.v.z2)),null===i.v.x&&(i.v.x=i.v.K),1<=Math.abs(i.v.x-i.v.K)&&(i.v.E=i.minMax(i.v.K/i.v.x*i.v.w,1,i.options.maxZoom),i.v.H=(i.v.o*i.v.E-i.v.k)/2,i.v.I=(i.v.n*i.v.E-i.v.j)/2,i.v.J=i.v.E-i.v.w,i.v.C=i.v.o*i.v.E<=i.v.k?0:i.minMax(i.v.q-(i.v.F-i.v.l-i.v.k/2-i.v.q)/(i.v.E-i.v.J)*i.v.J,-1*i.v.H,i.v.H),i.v.D=i.v.n*i.v.E<=i.v.j?0:i.minMax(i.v.r-(i.v.G-i.v.m-i.v.j/2-i.v.r)/(i.v.E-i.v.J)*i.v.J,-1*i.v.I,i.v.I),i.zoomPanElement(i.v.C+"px",i.v.D+"px",i.v.E),1<i.v.E&&(i.v.i=!0,i.p.g.style.opacity||"none"===i.p.g.style.display||i.fadeOut(i.p.g,200)),i.v.x=i.v.K,i.v.w=i.v.E,i.v.q=i.v.C,i.v.r=i.v.D)):(i.v.E=i.v.w,i.v.H=(i.v.o*i.v.E-i.v.k)/2,i.v.I=(i.v.n*i.v.E-i.v.j)/2,i.v.C=i.v.o*i.v.E<=i.v.k?0:i.minMax(i.v.y-(i.v.s-i.v.q),-1*i.v.H,i.v.H),i.v.D=i.v.n*i.v.E<=i.v.j?0:i.minMax(i.v.z-(i.v.t-i.v.r),-1*i.v.I,i.v.I),Math.abs(i.v.C)===Math.abs(i.v.H)&&(i.v.q=i.v.C,i.v.s=i.v.y),Math.abs(i.v.D)===Math.abs(i.v.I)&&(i.v.r=i.v.D,i.v.t=i.v.z),i.setZoomData(i.v.w,i.v.C,i.v.D),i.zoomPanElement(i.v.C+"px",i.v.D+"px",i.v.E))}if("mousemove"===t.type&&i.v.g){if("touchmove"==t.type)return!0;if(!1===i.v.p)return!1;i.v.y=t.clientX,i.v.z=t.clientY,i.v.E=i.v.w,i.v.H=(i.v.o*i.v.E-i.v.k)/2,i.v.I=(i.v.n*i.v.E-i.v.j)/2,i.v.C=i.v.o*i.v.E<=i.v.k?0:i.minMax(i.v.y-(i.v.s-i.v.q),-1*i.v.H,i.v.H),i.v.D=i.v.n*i.v.E<=i.v.j?0:i.minMax(i.v.z-(i.v.t-i.v.r),-1*i.v.I,i.v.I),Math.abs(i.v.C)===Math.abs(i.v.H)&&(i.v.q=i.v.C,i.v.s=i.v.y),Math.abs(i.v.D)===Math.abs(i.v.I)&&(i.v.r=i.v.D,i.v.t=i.v.z),i.setZoomData(i.v.w,i.v.C,i.v.D),i.zoomPanElement(i.v.C+"px",i.v.D+"px",i.v.E)}i.v.i||(i.v.d=t.pageX||t.touches[0].pageX,i.v.f=t.pageY||t.touches[0].pageY,i.v.a=i.v.c-i.v.d,i.v.b=i.v.e-i.v.f,i.options.animationSlide&&i.slide(0,-i.v.a+"px"))}),this.addEventListener(this.p.h,["touchend."+this.o,"mouseup."+this.o,"touchcancel."+this.o,"mouseleave."+this.o,"pointerup","pointercancel","MSPointerUp","MSPointerCancel"],function(t){if(i.d&&"touchend"===t.type&&(i.v.L=t.touches.length,0===i.v.L?(i.n&&i.setZoomData(i.v.w,i.v.C,i.v.D),1===i.v.w&&(i.v.i=!1,"none"===i.p.g.style.display&&i.fadeIn(i.p.g,200)),i.v.x=null,i.v.p=!1):1===i.v.L?(i.v.s=t.touches[0].clientX,i.v.t=t.touches[0].clientY):1<i.v.L&&(i.v.x=null)),i.v.g){var e=!(i.v.g=!1);i.options.loop||(0===i.nIndex&&i.v.a<0&&(e=!1),i.nIndex>=i.relatedElements.length-1&&0<i.v.a&&(e=!1)),Math.abs(i.v.a)>i.options.swipeTolerance&&e?i.loadImage(0<i.v.a?1:-1):i.options.animationSlide&&i.slide(i.options.animationSpeed/1e3,"0px"),i.options.swipeClose&&50<Math.abs(i.v.b)&&Math.abs(i.v.a)<i.options.swipeTolerance&&i.close()}}),this.addEventListener(this.p.h,["dblclick"],function(t){if(!i.d)return i.v.s=t.clientX,i.v.t=t.clientY,i.v.j=i.getDimensions(i.p.h).height,i.v.k=i.getDimensions(i.p.h).width,i.v.n=i.getDimensions(i.n).height,i.v.o=i.getDimensions(i.n).width,i.v.l=i.p.h.offsetLeft,i.v.m=i.p.h.offsetTop,i.n.classList.add("sl-transition"),i.v.i?(i.v.w=1,i.setZoomData(i.v.w,0,0),i.zoomPanElement("0px","0px",i.v.w),i.v.i=!1,"none"===i.p.g.style.display&&i.fadeIn(i.p.g,200)):(i.v.w=i.options.doubleTapZoom,i.setZoomData(i.v.w,0,0),i.zoomPanElement("0px","0px",i.v.w),i.p.g.style.opacity||"none"===i.p.g.style.display||i.fadeOut(i.p.g,200),i.v.i=!0),setTimeout(function(){i.n&&i.n.classList.remove("sl-transition")},200),!(i.v.p=!0)})}},{key:"getDimensions",value:function(t){var e=window.getComputedStyle(t),i=t.offsetHeight,n=t.offsetWidth,s=parseFloat(e.borderTopWidth);return{height:i-parseFloat(e.borderBottomWidth)-s-parseFloat(e.paddingTop)-parseFloat(e.paddingBottom),width:n-parseFloat(e.borderLeftWidth)-parseFloat(e.borderRightWidth)-parseFloat(e.paddingLeft)-parseFloat(e.paddingRight)}}},{key:"updateHash",value:function(){var t="pid="+(this.nIndex+1),e=window.location.href.split("#")[0]+"#"+t;this.k=!1,this.f?window.history[this.l?"replaceState":"pushState"]("",document.title,e):this.l?window.location.replace(e):window.location.hash=t,this.l||(this.j=!0),this.l=!0}},{key:"resetHash",value:function(){this.k=!0,this.j?history.back():this.f?history.pushState("",document.title,window.location.pathname+window.location.search):window.location.hash="",clearTimeout(this.m)}},{key:"updateURL",value:function(){clearTimeout(this.m),this.l?this.m=setTimeout(this.updateHash.bind(this),800):this.updateHash()}},{key:"setCaption",value:function(t,e){var i=this;this.options.captions&&t&&""!==t&&void 0!==t&&(this.hide(this.p.g),this.p.g.style.width=e+"px",this.p.g.innerHTML=t,this.p.h.appendChild(this.p.g),setTimeout(function(){i.fadeIn(i.p.g,300)},this.options.captionDelay))}},{key:"slide",value:function(t,e){if(!this.c)return this.p.h.style.left=e;this.p.h.style[this.b+"transform"]="translateX("+e+")",this.p.h.style[this.b+"transition"]=this.b+"transform "+t+"s linear"}},{key:"getRelated",value:function(e){return e&&!1!==e&&"nofollow"!==e?Array.from(this.elements).filter(function(t){return t.getAttribute("rel")===e}):this.elements}},{key:"openImage",value:function(t){var e=this;t.dispatchEvent(new Event("show."+this.o)),this.options.disableScroll&&(this.u=this.toggleScrollbar("hide")),this.options.htmlClass&&""!==this.options.htmlClass&&document.querySelector("html").classList.add(this.options.htmlClass),document.body.appendChild(this.p.a),this.p.a.appendChild(this.p.h),this.options.overlay&&document.body.appendChild(this.p.e),this.relatedElements=this.getRelated(t.rel),this.options.showCounter&&(1==this.relatedElements.length&&this.p.a.contains(this.p.f)?this.p.a.removeChild(this.p.f):1<this.relatedElements.length&&!this.p.a.contains(this.p.f)&&this.p.a.appendChild(this.p.f)),this.h=!0,this.nIndex=this.relatedElements.indexOf(t);var i=t.getAttribute(this.options.sourceAttr);this.n=document.createElement("img"),this.n.style.display="none",this.n.setAttribute("src",i),this.n.dataset.scale=1,this.n.dataset.translateX=0,this.n.dataset.translateY=0,-1===this.q.indexOf(i)&&this.q.push(i),this.p.h.innerHTML="",this.p.h.setAttribute("style",""),this.p.h.appendChild(this.n),this.fadeIn(this.p.e,300),this.fadeIn([this.p.f,this.p.c,this.p.b],300),this.show(this.p.d),this.p.f.querySelector(".sl-current").innerHTML=this.nIndex+1,this.p.f.querySelector(".sl-total").innerHTML=this.relatedElements.length,this.adjustImage(),this.options.preloading&&this.preload(),setTimeout(function(){t.dispatchEvent(new Event("shown."+e.o))},this.options.animationSpeed)}},{key:"addEventListener",value:function(t,e,i,n){t=this.wrap(t),e=this.wrap(e);var s,o=_createForOfIteratorHelper(t);try{for(o.s();!(s=o.n()).done;){var a=s.value;a.namespaces||(a.namespaces={});var r,l=_createForOfIteratorHelper(e);try{for(l.s();!(r=l.n()).done;){var h=r.value,v=n||!1;a.namespaces[h]=i,a.addEventListener(h.split(".")[0],i,v)}}catch(t){l.e(t)}finally{l.f()}}}catch(t){o.e(t)}finally{o.f()}}},{key:"removeEventListener",value:function(t,e){t=this.wrap(t),e=this.wrap(e);var i,n=_createForOfIteratorHelper(t);try{for(n.s();!(i=n.n()).done;){var s,o=i.value,a=_createForOfIteratorHelper(e);try{for(a.s();!(s=a.n()).done;){var r=s.value;o.removeEventListener(r.split(".")[0],o.namespaces[r]),delete o.namespaces[r]}}catch(t){a.e(t)}finally{a.f()}}}catch(t){n.e(t)}finally{n.f()}}},{key:"fadeOut",value:function(r,t,l){var e,h=this,i=_createForOfIteratorHelper(r=this.wrap(r));try{for(i.s();!(e=i.n()).done;){e.value.style.opacity=1}}catch(t){i.e(t)}finally{i.f()}var v=16.66666/(t||300);!function t(){var e=parseFloat(r[0].style.opacity);if((e-=v)<0){var i,n=_createForOfIteratorHelper(r);try{for(n.s();!(i=n.n()).done;){var s=i.value;s.style.display="none",s.style.opacity=""}}catch(t){n.e(t)}finally{n.f()}l&&l.call(h,r)}else{var o,a=_createForOfIteratorHelper(r);try{for(a.s();!(o=a.n()).done;){o.value.style.opacity=e}}catch(t){a.e(t)}finally{a.f()}requestAnimationFrame(t)}}()}},{key:"fadeIn",value:function(a,t,r,e){var i,l=this,n=_createForOfIteratorHelper(a=this.wrap(a));try{for(n.s();!(i=n.n()).done;){var s=i.value;s.style.opacity=0,s.style.display=e||"block"}}catch(t){n.e(t)}finally{n.f()}var h=parseFloat(a[0].dataset.opacityTarget||1),v=16.66666*h/(t||300);!function t(){var e=parseFloat(a[0].style.opacity);if((e+=v)>h){var i,n=_createForOfIteratorHelper(a);try{for(n.s();!(i=n.n()).done;){i.value.style.opacity=""}}catch(t){n.e(t)}finally{n.f()}r&&r.call(l,a)}else{var s,o=_createForOfIteratorHelper(a);try{for(o.s();!(s=o.n()).done;){s.value.style.opacity=e}}catch(t){o.e(t)}finally{o.f()}requestAnimationFrame(t)}}()}},{key:"hide",value:function(t){var e,i=_createForOfIteratorHelper(t=this.wrap(t));try{for(i.s();!(e=i.n()).done;){var n=e.value;n.dataset.initialDisplay=n.style.display,n.style.display="none"}}catch(t){i.e(t)}finally{i.f()}}},{key:"show",value:function(t,e){var i,n=_createForOfIteratorHelper(t=this.wrap(t));try{for(n.s();!(i=n.n()).done;){var s=i.value;s.style.display=s.dataset.initialDisplay||e||"block"}}catch(t){n.e(t)}finally{n.f()}}},{key:"wrap",value:function(t){return"function"==typeof t[Symbol.iterator]&&"string"!=typeof t?t:[t]}},{key:"on",value:function(t,e){t=this.wrap(t);var i,n=_createForOfIteratorHelper(this.elements);try{for(n.s();!(i=n.n()).done;){var s=i.value;s.fullyNamespacedEvents||(s.fullyNamespacedEvents={});var o,a=_createForOfIteratorHelper(t);try{for(a.s();!(o=a.n()).done;){var r=o.value;s.fullyNamespacedEvents[r]=e,s.addEventListener(r,e)}}catch(t){a.e(t)}finally{a.f()}}}catch(t){n.e(t)}finally{n.f()}return this}},{key:"off",value:function(t){t=this.wrap(t);var e,i=_createForOfIteratorHelper(this.elements);try{for(i.s();!(e=i.n()).done;){var n,s=e.value,o=_createForOfIteratorHelper(t);try{for(o.s();!(n=o.n()).done;){var a=n.value;void 0!==s.fullyNamespacedEvents&&a in s.fullyNamespacedEvents&&s.removeEventListener(a,s.fullyNamespacedEvents[a])}}catch(t){o.e(t)}finally{o.f()}}}catch(t){i.e(t)}finally{i.f()}return this}},{key:"open",value:function(t){t=t||this.elements[0],"undefined"!=typeof jQuery&&t instanceof jQuery&&(t=t.get(0)),this.r=this.elements.indexOf(t),-1<this.r&&this.openImage(t)}},{key:"next",value:function(){this.loadImage(1)}},{key:"prev",value:function(){this.loadImage(-1)}},{key:"destroy",value:function(){this.off(["close."+this.o,"closed."+this.o,"nextImageLoaded."+this.o,"prevImageLoaded."+this.o,"change."+this.o,"nextDone."+this.o,"prevDone."+this.o,"error."+this.o,"changed."+this.o,"next."+this.o,"prev."+this.o,"show."+this.o,"shown."+this.o]),this.removeEventListener(this.elements,"click."+this.o),this.removeEventListener(document.body,"contextmenu."+this.o),this.removeEventListener(document.body,"keyup."+this.o),this.removeEventListener(this.p.c.getElementsByTagName("button"),"click."+this.o),this.removeEventListener(this.p.b,"click."+this.o),this.removeEventListener(window,"resize."+this.o),this.removeEventListener(window,"hashchange."+this.o),this.close(),this.g&&(document.body.removeChild(this.p.a),document.body.removeChild(this.p.e)),this.elements=null}},{key:"refresh",value:function(){if(!this.t)throw"refreshing only works when you initialize using a selector!";var t=this.options,e=this.t;return this.destroy(),this.constructor(e,t),this}},{key:"hash",get:function(){return window.location.hash.substring(1)}}]),n}();!function(t){t.fn.simpleLightbox=function(t){return new SimpleLightbox(this.get(),t)}}(jQuery,(window,document));