.dropdown-submenu.pull-left>.dropdown-menu {
    border-radius: 6px;
    border-top-left-radius: 0;
    margin-top: 0.2px;
}

.dropdown-submenu {
    position: relative;
}

.dropdown-menu>li>a {
    --tw-text-opacity: 1;
    color: rgb(15 23 42/var(--tw-text-opacity));
    overflow-wrap: break-word;
    padding: 0.375rem 1rem;
    white-space: normal;
    width: 100%;
}

.dropdown-menu>.active>a, .dropdown-menu>.active>a:focus, .dropdown-menu>.active>a:hover, .dropdown-menu>li>a:focus, .dropdown-menu>li>a:hover {
    --tw-bg-opacity: 1;
    --tw-text-opacity: 1;
    background-color: rgb(241 245 249/var(--tw-bg-opacity));
    color: rgb(15 23 42/var(--tw-text-opacity));
}

.dropdown-menu>li>a:focus, .dropdown-menu>li>a:hover {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.dropdown-menu>li>a:focus, .dropdown-menu>li>a:hover {
    color: #262626;
    text-decoration: none;
    background-color: #f5f5f5;
}

.dropdown-menu>li>a {
    --tw-text-opacity: 1;
    color: rgb(15 23 42/var(--tw-text-opacity));
    overflow-wrap: break-word;
    padding: 0.375rem 1rem;
    white-space: normal;
    width: 100%;
}

.dropdown-menu>li>a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: 400;
    line-height: 1.42857143;
    color: #333;
    white-space: nowrap;
}

.dropdown-submenu>.dropdown-menu {
    border-radius: 0 6px 6px 6px;
    left: 100%;
    margin-left: -1px;
    margin-top: -6px;
    max-height: 500px;
    overflow-y: auto;
    top: 0;
}

.dropdown-menu {
    z-index: 10000;
}

.open>.dropdown-menu {
    display: block;
}

.navbar-right .dropdown-menu {
    right: 0;
    left: auto;
}

.open>.dropdown-menu {
    display: block;
}

.navbar-nav>li>.dropdown-menu {
    margin-top: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.btn-bottom-toolbar {
    position: fixed;
    bottom: 0;
    padding: 15px;
    padding-right: 41px;
    margin: 0 0 0 -46px;
    -webkit-box-shadow: 0 -4px 1px -4px rgb(0 0 0 / 10%);
    box-shadow: 0 -4px 1px -4px rgb(0 0 0 / 10%);
    background: #fff;
    width: calc(100% - 211px);
    z-index: 5;
    border-top: 1px solid #ededed;
}

.navbar-nav>li>.dropdown-menu {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgb(0 0 0 / 18%);
    box-shadow: 0 6px 12px rgb(0 0 0 / 18%);
}

.dropdown-menu {
    --tw-bg-opacity: 1;
    --tw-shadow: 0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    --tw-ring-color: rgba(15,23,42,.05);
    --tw-ring-opacity: 0.05;
    background-color: rgb(255 255 255/var(--tw-bg-opacity));
    border: 0;
    border-radius: 0.5rem;
    box-shadow: var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);
    box-shadow: var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000);
    margin-top: 0.75rem;
    z-index: 9000;
}

.panel, .panel-body, .panel_s {
    background-color: rgb(255 255 255/var(--tw-bg-opacity));
    border-radius: 0.375rem;
}

.panel, .panel_s {
    --tw-border-opacity: 1;
    --tw-bg-opacity: 1;
    --tw-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    border-color: rgb(226 232 240/var(--tw-border-opacity));
    border-style: solid;
    border-width: 1px;
    box-shadow: var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);
    margin-bottom: 25px;
}

.progress-vendor {
    background-color: #f9fafc;
}

.progress-vendor  {
    height: 20px;
    margin-bottom: 20px;
    overflow: hidden;
    background-color: #f5f5f5;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 2px rgb(0 0 0 / 10%);
    box-shadow: inset 0 1px 2px rgb(0 0 0 / 10%);
}

.pur_input_none{
    background: 0 0 !important;
    border: unset !important;
}